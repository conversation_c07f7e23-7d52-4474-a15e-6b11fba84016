// NimBLE Client - Scan

#include <stdio.h>
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include "freertos/event_groups.h"
#include "esp_event.h"
#include "nvs_flash.h"
#include "esp_log.h"
#include "nimble/nimble_port.h"
#include "nimble/nimble_port_freertos.h"
#include "host/ble_hs.h"
#include "host/ble_uuid.h"
#include "services/gap/ble_svc_gap.h"
#include "sdkconfig.h"

char *TAG = "BLE Client Scan";
uint8_t ble_addr_type;
static int scan_device_count = 0;
static const int MAX_SCAN_DEVICES = 10;  // 最大扫描设备数量
static const int SCAN_DURATION_MS = 10000;  // 扫描持续时间：10秒
void ble_app_scan(void);

// BLE event handling
static int ble_gap_event(struct ble_gap_event *event, void *arg)
{
    struct ble_hs_adv_fields fields;

    switch (event->type)
    {
    // NimBLE event discovery
    case BLE_GAP_EVENT_DISC:
        ESP_LOGI("GAP", "GAP EVENT DISCOVERY");
        ble_hs_adv_parse_fields(&fields, event->disc.data, event->disc.length_data);
        if (fields.name_len > 0)
        {
            printf("Device %d - Name: %.*s\n", scan_device_count + 1, fields.name_len, fields.name);
            // Print 16-bit UUIDs if available
            if (fields.num_uuids16 > 0) {
                printf("UUIDs: ");
                for (int i = 0; i < fields.num_uuids16; i++) {
                    printf("0x%04X ", ble_uuid_u16(&fields.uuids16[i].u));
                }
                printf("\n");
            }
            else if(fields.num_uuids32 > 0) {
                printf("32-bit UUIDs: ");
                for (int i = 0; i < fields.num_uuids32; i++) {
                    printf("0x%08lX ", (unsigned long)fields.uuids32[i].value);
                }
                printf("\n");
            }
            else if(fields.num_uuids128 > 0) {
                printf("128-bit UUIDs: ");
                for (int i = 0; i < fields.num_uuids128; i++) {
                    printf("0x");
                    for (int j = 0; j < 16; j++) {
                        printf("%02X", fields.uuids128[i].value[j]);
                    }
                    printf(" ");
                }
                printf("\n");
            }
        }
        else
        {
            printf("Device %d - No name available\n", scan_device_count + 1);
        }
        printf("FULL fields");
        scan_device_count++;

        // 如果达到最大扫描设备数量，停止扫描
        if (scan_device_count >= MAX_SCAN_DEVICES)
        {
            printf("Reached maximum scan devices (%d), stopping scan...\n", MAX_SCAN_DEVICES);
            ble_gap_disc_cancel();
        }
        break;

    case BLE_GAP_EVENT_DISC_COMPLETE:
        printf("Scan completed. Total devices found: %d\n", scan_device_count);
        printf("Scan finished.\n");
        break;

    default:
        break;
    }
    return 0;
}

void ble_app_scan(void)
{
    printf("Start single scan (duration: %d ms, max devices: %d)...\n", SCAN_DURATION_MS, MAX_SCAN_DEVICES);

    // 重置扫描计数器
    scan_device_count = 0;

    struct ble_gap_disc_params disc_params;
    disc_params.filter_duplicates = 1;
    disc_params.passive = 0;
    disc_params.itvl = 0;
    disc_params.window = 0;
    disc_params.filter_policy = 0;
    disc_params.limited = 0;

    // 使用有限时间扫描而不是永久扫描
    ble_gap_disc(ble_addr_type, SCAN_DURATION_MS, &disc_params, ble_gap_event, NULL);
}

// The application
void ble_app_on_sync(void)
{
    ble_hs_id_infer_auto(0, &ble_addr_type); // Determines the best address type automatically
    ble_app_scan();                          
}

// The infinite task
void host_task(void *param)
{
    nimble_port_run(); // This function will return only when nimble_port_stop() is executed
}

void app_main()
{
    nvs_flash_init();                               // 1 - Initialize NVS flash using
    nimble_port_init();                             // 2 - Initialize the host and controller stack
    ble_svc_gap_device_name_set("BLE-Scan-Client"); // 3 - Set device name characteristic
    ble_svc_gap_init();                             // 3 - Initialize GAP service
    ble_hs_cfg.sync_cb = ble_app_on_sync;           // 4 - Set application
    nimble_port_freertos_init(host_task);           // 5 - Set infinite task
}